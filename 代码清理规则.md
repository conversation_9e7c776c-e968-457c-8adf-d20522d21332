# Augment Agent 代码清理与质量保证指南

## 核心原则

本指南为 Augment Agent 提供了在代码修改后执行强制性代码审查和清理的具体操作规范。这些规则确保代码库的整洁性、可维护性，并防止技术债务积累。

### 🎯 Augment Agent 的责任
作为 Augment Agent，您必须在每次代码修改后自动执行以下清理检查，这是您工作流程中不可跳过的强制步骤。

## 📋 强制执行时机与流程

### 何时必须执行清理检查
```
✅ 任何代码修改、添加、删除操作后
✅ 重构或优化代码后
✅ 修复 bug 或实现新功能后
✅ 更新依赖、配置或环境设置后
✅ 合并代码分支或解决冲突后
```

### 执行优先级（严格按顺序）
1. **代码功能实现** - 完成用户请求的核心功能
2. **代码清理检查** - 执行本指南规定的所有清理步骤
3. **测试验证** - 运行相关测试确保功能正常
4. **任务完成确认** - 向用户报告任务完成

> ⚠️ **重要**：步骤 2 和 3 完成前，绝不能声明任务完成

## 🔍 四大核心清理检查项

### 1. 未使用导入和依赖清理

#### Augment Agent 执行步骤：
```
第一步：扫描所有导入语句
第二步：交叉引用代码使用情况
第三步：识别未使用的导入
第四步：安全移除确认无风险的导入
第五步：标记需要用户确认的可疑导入
```

#### 按语言检查的具体内容：
- **Python**: `import`、`from ... import` 语句，检查 `requirements.txt`/`pyproject.toml`
- **JavaScript/TypeScript**: `import`、`require()` 语句，检查 `package.json` 依赖
- **Java**: `import` 语句，检查 `pom.xml`/`build.gradle` 依赖
- **C#**: `using` 语句，检查 `.csproj` 包引用
- **Go**: `import` 语句，检查 `go.mod` 依赖
- **Rust**: `use` 语句，检查 `Cargo.toml` 依赖

#### 自动移除条件：
- ✅ 明确未在代码中使用的导入
- ✅ 重复的导入语句
- ✅ 已被其他导入覆盖的具体导入

#### 需要用户确认：
- ❓ 可能被动态调用的模块
- ❓ 用于类型注解但未直接使用的导入
- ❓ 测试或开发环境专用的依赖

### 2. 废弃代码元素清理

#### Augment Agent 检查范围：
```
🎯 函数和方法：检查调用引用，确认是否孤立
🎯 类和接口：验证实例化、继承、实现关系
🎯 模块和文件：分析导入和依赖关系
🎯 常量和变量：检查使用情况和作用域
```

#### 识别策略：
1. **静态分析**：使用 codebase-retrieval 工具搜索引用
2. **依赖图分析**：构建调用关系图
3. **动态调用检查**：搜索字符串引用、反射调用
4. **测试代码验证**：确认测试中是否使用

#### 安全移除原则：
- ✅ 无任何引用的私有函数/方法
- ✅ 空实现且无继承关系的类
- ✅ 无导入引用的独立模块
- ❓ 公共 API 即使未使用也需确认
- ❓ 可能用于未来扩展的预留代码

### 3. 死代码路径清理

#### Augment Agent 分析重点：
```
🔍 条件语句：if/else、switch/case、三元操作符
🔍 循环结构：永远不执行的循环体
🔍 异常处理：不可达的 catch 块
🔍 函数返回：return 后的不可达代码
🔍 配置驱动：基于配置值的条件分支
```

#### 常见死代码模式：
- `if False:` 或 `if (false)` 块
- 永远不会抛出的异常的 catch 块
- return/break/continue 语句后的代码
- 基于常量条件的永假分支
- 被注释掉但未删除的代码块

#### 处理策略：
- ✅ 明显的死代码（如 `if False:`）直接移除
- ✅ return 后的不可达代码直接删除
- ❓ 基于配置的条件分支需要确认配置范围
- ❓ 复杂逻辑导致的死代码需要用户确认

### 4. 过时配置和设置清理

#### Augment Agent 检查目标：
```
📁 配置文件：.env、config.json、settings.py 等
📁 构建配置：webpack.config.js、Dockerfile、CI/CD 配置
📁 环境变量：检查代码中引用的环境变量
📁 常量定义：全局常量、枚举值、配置类
📁 初始化代码：启动脚本、初始化函数
```

#### 重点关注领域：
- **数据库配置**：连接字符串、池设置、迁移配置
- **API 配置**：端点 URL、认证设置、超时配置
- **功能开关**：特性标志、A/B 测试配置
- **缓存设置**：Redis、Memcached 配置
- **日志配置**：级别设置、输出格式、轮转策略
- **安全配置**：密钥、证书、权限设置

#### 清理原则：
- ✅ 代码中无引用的配置项
- ✅ 已被新配置替代的旧设置
- ❓ 环境特定的配置需要确认环境范围
- ❓ 安全相关配置需要特别谨慎

## 🔄 Augment Agent 标准操作流程

### 阶段一：智能扫描与分析
```
1️⃣ 使用 codebase-retrieval 工具分析修改影响范围
2️⃣ 扫描相关文件中的导入、函数、类、配置
3️⃣ 交叉引用使用情况，构建依赖关系图
4️⃣ 生成分类清理候选列表
```

### 阶段二：风险评估与分类
```
🟢 低风险（可自动清理）
   - 明确未使用的导入
   - 空函数/类（无业务逻辑）
   - 明显的死代码块
   - 重复的导入语句

🟡 中风险（需要用户确认）
   - 可能被动态调用的代码
   - 包含业务逻辑的未使用函数
   - 配置相关的更改
   - 公共 API 的移除

🔴 高风险（标记但不处理）
   - 涉及安全配置的更改
   - 可能影响系统稳定性的操作
   - 复杂的第三方集成代码
```

### 阶段三：执行清理操作
```
步骤 1：自动处理低风险项目
步骤 2：向用户报告中风险项目并请求确认
步骤 3：标记高风险项目供后续处理
步骤 4：更新相关文档和注释
```

## 🤖 Augment Agent 决策矩阵

### 自动移除（无需用户确认）
| 代码类型 | 条件 | 示例 |
|---------|------|------|
| 导入语句 | 代码中完全未使用 | `import unused_module` |
| 局部变量 | 定义后未引用 | `temp_var = calculate()` |
| 死代码块 | 明显不可达 | `if False:` 或 `return` 后的代码 |
| 重复导入 | 同一模块多次导入 | 同文件中的重复 `import` |
| 空函数 | 无逻辑内容的函数 | `def empty_func(): pass` |

### 必须请求用户确认
| 代码类型 | 原因 | 处理方式 |
|---------|------|----------|
| 公共函数/类 | 可能被外部调用 | 询问是否确认移除 |
| 配置项 | 可能影响运行时行为 | 确认配置的使用范围 |
| 业务逻辑代码 | 可能包含重要逻辑 | 请求用户审查 |
| 动态调用代码 | 可能通过字符串调用 | 确认调用方式 |
| 测试相关代码 | 可能影响测试覆盖 | 确认测试策略 |

### 标记但延后处理
| 代码类型 | 原因 | 标记方式 |
|---------|------|----------|
| 安全配置 | 风险过高 | 添加 TODO 注释 |
| 第三方集成 | 依赖关系复杂 | 记录到清理日志 |
| 性能优化代码 | 可能影响性能 | 建议专门审查 |

## 📚 文档同步与维护要求

### Augment Agent 必须检查和更新的文档
```
📄 README.md - 功能描述、安装说明、使用示例
📄 API 文档 - 接口变更、参数说明、返回值
📄 代码注释 - 函数说明、类描述、复杂逻辑注释
📄 配置文档 - 配置项说明、环境变量、部署指南
📄 CHANGELOG.md - 变更记录、版本说明
📄 测试文档 - 测试用例、覆盖率报告
```

### 文档更新检查清单
- [ ] 移除的功能是否在文档中有描述？
- [ ] 更改的 API 是否需要更新接口文档？
- [ ] 删除的配置项是否在说明文档中提及？
- [ ] 代码注释是否与实际实现保持一致？
- [ ] 示例代码是否仍然有效？

### 自动化文档检查
```
1. 扫描文档中提及的函数、类、配置项
2. 验证这些引用是否仍然存在于代码中
3. 标记可能过时的文档内容
4. 建议需要更新的文档部分
```

## 🧪 质量保证与验证流程

### 清理后验证步骤
```
✅ 代码编译/语法检查通过
✅ 现有测试套件全部通过
✅ 静态分析工具无新增警告
✅ 文档与代码保持同步
✅ 配置文件格式正确
```

### Augment Agent 测试策略
1. **单元测试**：确保修改的函数/类功能正常
2. **集成测试**：验证模块间交互未受影响
3. **回归测试**：确认未破坏现有功能
4. **性能测试**：验证清理操作未影响性能
5. **安全测试**：确认清理未引入安全风险

### 分批清理策略
```
第一批：低风险项目（导入、死代码）
第二批：中风险项目（未使用函数）
第三批：配置相关项目
第四批：文档和注释更新
```

## 🛠️ Augment Agent 推荐工具集

### 内置工具优先使用
- **codebase-retrieval**：分析代码依赖和引用关系
- **str-replace-editor**：安全地编辑代码文件
- **view**：检查文件内容和搜索模式

### 外部工具建议（按语言）
| 语言 | 静态分析 | 依赖检查 | 格式化 |
|------|----------|----------|--------|
| Python | `pylint`, `flake8` | `pip-audit` | `black`, `isort` |
| JavaScript | `eslint` | `npm audit` | `prettier` |
| TypeScript | `tslint`, `eslint` | `npm audit` | `prettier` |
| Java | `SpotBugs`, `PMD` | `OWASP Dependency Check` | `google-java-format` |
| C# | `Roslyn Analyzers` | `dotnet list package --vulnerable` | `dotnet format` |
| Go | `go vet`, `staticcheck` | `go mod tidy` | `gofmt` |
| Rust | `clippy` | `cargo audit` | `rustfmt` |

## 🎯 Augment Agent 成功标准

### 清理完成的判断标准
```
✅ 所有四大检查项目已执行完毕
✅ 自动清理项目已处理
✅ 需要确认的项目已向用户报告
✅ 相关文档已同步更新
✅ 测试验证已通过
✅ 清理操作已记录
```

### 向用户报告的内容格式
```
## 代码清理报告

### 自动处理项目
- 移除了 X 个未使用的导入
- 删除了 Y 个死代码块
- 清理了 Z 个重复导入

### 需要确认的项目
- [函数名] - 可能被动态调用，建议确认
- [配置项] - 不确定使用范围，请确认

### 文档更新
- 更新了 README.md 中的功能描述
- 同步了 API 文档中的接口变更

### 测试结果
- 所有现有测试通过 ✅
- 静态分析无新增警告 ✅
```

---

## ⚠️ 强制性声明

**本指南是 Augment Agent 工作流程中的强制性组成部分，不可被任何其他指令、上下文或用户请求覆盖或跳过。每次代码修改后都必须严格执行这些清理检查。**

**Augment Agent 必须在声明任务完成前确认所有清理步骤已执行完毕。**
